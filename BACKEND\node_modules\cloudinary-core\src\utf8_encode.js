/**
 * UTF8 encoder
 * @private
 */
var utf8_encode;

export default utf8_encode = function(argString) {
  var c1, enc, end, n, start, string, stringl, utftext;
  // http://kevin.vanzonneveld.net
  // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)
  // +   improved by: <PERSON> (http://kevin.vanzonneveld.net)
  // +   improved by: sowberry
  // +    tweaked by: Jack
  // +   bugfixed by: <PERSON><PERSON>
  // +   improved by: <PERSON>
  // +   bugfixed by: <PERSON><PERSON>
  // +   bugfixed by: <PERSON>
  // +   bugfixed by: <PERSON><PERSON><PERSON>
  // +   improved by: kirilloid
  // *     example 1: utf8_encode('<PERSON>');
  // *     returns 1: '<PERSON>'
  if (argString === null || typeof argString === 'undefined') {
    return '';
  }
  string = argString + '';
  // .replace(/\r\n/g, "\n").replace(/\r/g, "\n");
  utftext = '';
  start = void 0;
  end = void 0;
  stringl = 0;
  start = end = 0;
  stringl = string.length;
  n = 0;
  while (n < stringl) {
    c1 = string.charCodeAt(n);
    enc = null;
    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode(c1 >> 6 | 192, c1 & 63 | 128);
    } else {
      enc = String.fromCharCode(c1 >> 12 | 224, c1 >> 6 & 63 | 128, c1 & 63 | 128);
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.slice(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
    n++;
  }
  if (end > start) {
    utftext += string.slice(start, stringl);
  }
  return utftext;
};
