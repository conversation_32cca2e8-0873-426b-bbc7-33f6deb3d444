{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "dev:local": "cp .env.local .env && vite --host", "dev:prod": "cp .env.production .env && vite --host", "build": "vite build", "build:prod": "cp .env.production .env && vite build", "lint": "eslint .", "preview": "vite preview", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@tailwindcss/vite": "^4.1.8", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}