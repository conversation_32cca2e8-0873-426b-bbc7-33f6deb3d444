{"name": "devconnect", "version": "1.0.0", "description": "DevConnect - Social platform for developers", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd BACKEND && npm run dev", "dev:frontend": "cd FRONTEND && npm run dev:local", "dev:frontend:prod": "cd FRONTEND && npm run dev:prod", "build": "cd FRONTEND && npm run build:prod", "install:all": "npm install && cd BACKEND && npm install && cd ../FRONTEND && npm install", "clean:all": "cd BACKEND && npm run clean && cd ../FRONTEND && npm run clean", "start:prod": "cd BACKEND && npm start"}, "devDependencies": {"concurrently": "^9.1.2"}, "keywords": ["social", "developers", "react", "node", "express", "mongodb"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC"}